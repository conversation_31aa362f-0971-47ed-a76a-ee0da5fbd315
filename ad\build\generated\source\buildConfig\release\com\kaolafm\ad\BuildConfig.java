/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.kaolafm.ad;

public final class BuildConfig {
  public static final boolean DEBUG = false;
  public static final String LIBRARY_PACKAGE_NAME = "com.kaolafm.ad";
  /**
   * @deprecated APPLICATION_ID is misleading in libraries. For the library package name use LIBRARY_PACKAGE_NAME
   */
  @Deprecated
  public static final String APPLICATION_ID = "com.kaolafm.ad";
  public static final String BUILD_TYPE = "release";
  public static final String FLAVOR = "";
  public static final int VERSION_CODE = 10200;
  public static final String VERSION_NAME = "1.2.0";
  // Fields from the variant
  public static final boolean INCLUDE_OLD_SDK = true;
  // Fields from build type: release
  public static final String API_VERSION = "v3";
  public static final String DOMAIN_TYPE = "";
}
